buildscript {
    repositories {
        mavenCentral()
        google()
//        maven { url 'https://maven.testfairy.com' }
//        maven { url 'https://maven.google.com' }
        maven { url 'https://plugins.gradle.org/m2/' } // Gradle Plugin Portal
    }
    dependencies {
        classpath 'com.google.gms:google-services:4.3.10'
    }
}

repositories {
//    maven { url 'https://maven.google.com' }
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace 'br.com.cliente.minhaunimed'
    compileSdkVersion 35
    ndkVersion = "26.1.10909125"

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
    }

     kotlinOptions {
        jvmTarget = '17'
    }


    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "br.com.cliente.minhaunimed"
        minSdkVersion 24
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    } 
 
    buildTypes {
        release {
            signingConfig signingConfigs.release

            /////////////////////////
            // Added jitsi meeting //
            /////////////////////////
            minifyEnabled true
            shrinkResources true
            //useProguard true
            // https://developer.android.com/studio/build/shrink-code
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    packagingOptions {
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.google.firebase:firebase-analytics:17.2.0'
    implementation 'androidx.work:work-runtime:2.7.0'
    implementation 'com.github.kittinunf.fuel:fuel:2.2.1'
    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin:2.10.+'
    implementation 'com.beust:klaxon:5.5'
    implementation "us.zoom.videosdk:zoomvideosdk-core:1.9.0"
    implementation "us.zoom.videosdk:zoomvideosdk-videoeffects:1.9.0"
    implementation "us.zoom.videosdk:zoomvideosdk-annotation:1.9.0"

//    Firebase ML Vision dependencies
    implementation 'com.google.android.gms:play-services-location:21.3.0'   
    //api 'com.google.android.gms:play-services-vision:20.1.2'
    //api 'com.google.android.gms:play-services-vision-common:19.1.2'
    //api 'com.google.firebase:firebase-ml-vision-image-label-model:20.0.2'
    //api 'com.google.android.gms:play-services-vision-image-labeling-internal:16.0.5'
    //api 'com.google.android.gms:play-services-vision-image-label:18.1.0'
    //api 'com.google.firebase:firebase-ml-vision-face-model:20.0.2'
    //api 'com.google.android.gms:play-services-vision-face-contour-internal:16.0.3'

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'


}

apply plugin: 'com.google.gms.google-services'
