import 'package:cliente_minha_unimed/models/res/procedures/res_attendence_model.dart';
import 'package:equatable/equatable.dart';


abstract class ResProceduresState extends Equatable {
  const ResProceduresState();

  @override
  List<Object> get props => [];
}

class InitialResProceduresState extends ResProceduresState {}

class LoadingResProceduresState extends ResProceduresState {}

class ErrorResProceduresState extends ResProceduresState {
  final String message;

  const ErrorResProceduresState({required this.message});

  @override
  List<Object> get props => [message];
}

class LoadedResProceduresState extends ResProceduresState {
  final List<ResAttendanceModel> listResProcedures;

  const LoadedResProceduresState({required this.listResProcedures});

  @override
  List<Object> get props => [listResProcedures];
}

class NoDataResProceduresSearchState extends ResProceduresState {
  @override
  List<Object> get props => [];
}

class ErrorResProcedureSearchState extends ResProceduresState {
  final String message;

  const ErrorResProcedureSearchState({required this.message});

  @override
  List<Object> get props => [message];
}