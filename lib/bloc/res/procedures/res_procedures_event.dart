import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

abstract class ResProceduresEvent extends Equatable {
  const ResProceduresEvent();

  @override
  List<Object> get props => [];
}

class ListResProceduresEvent extends ResProceduresEvent {
  final String crm;
  final String card;
  final DateTimeRange? dataRange;

  const ListResProceduresEvent({
    required this.crm,
    required this.card,
    this.dataRange,
  });

  @override
  List<Object> get props => [crm, card, dataRange ?? DateTimeRange(start: DateTime(0), end: DateTime(0))];
}