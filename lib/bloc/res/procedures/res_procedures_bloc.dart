import 'package:cliente_minha_unimed/models/res/procedures/res_attendence_model.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../shared/api/graphql.api.dart';
import 'res_procedures_event.dart';
import 'res_procedures_state.dart';

class ResProceduresBloc extends Bloc<ResProceduresEvent, ResProceduresState> {
  ResProceduresBloc() : super(InitialResProceduresState());

  List<ResAttendanceModel> _listProcedures = List.empty(growable: true);
  List<ResAttendanceModel> get listProcedures => _listProcedures;

  @override
  Stream<ResProceduresState> mapEventToState(ResProceduresEvent event) async* {
    if (event is ListResProceduresEvent) {
      yield LoadingResProceduresState();
      try {
     _listProcedures = await Locator.instance<GraphQlApi>()
            .resGetProcedureClient(card: event.card,  dataRange: event.dataRange);
 
      
      
        if (_listProcedures.isEmpty) {
          yield NoDataResProceduresSearchState();
        } else {
          yield LoadedResProceduresState(listResProcedures: _listProcedures);
        }
      } catch (e) {
        yield ErrorResProceduresState(message: e.toString());
      }
    }
  }
}