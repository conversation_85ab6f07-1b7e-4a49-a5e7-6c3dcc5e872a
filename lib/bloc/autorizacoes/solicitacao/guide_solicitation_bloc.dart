import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_state.dart';
import 'package:cliente_minha_unimed/shared/api/protocols.graphql.dart';
import 'package:cliente_minha_unimed/shared/api/solicitacoes.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class GuideSolicitationBloc
    extends Bloc<GuideSolicitationEvent, GuideSolicitationState> {
  final logger = UnimedLogger(className: 'GuideBloc');

  GuideSolicitationBloc() : super(InitialGuideState());

  @override
  Stream<GuideSolicitationState> mapEventToState(
    GuideSolicitationEvent event,
  ) async* {
    if (event is GetGuidesPdf) {
      yield LoadingGuideSolicitationState();

      try {
        final result =
            await Locator.instance.get<SolicitacoesApi>().getGuidesPdf(
                  numProtocolo: event.numProtocolo,
                  numAtend: event.numAtend,
                );

        final List<String> pathFiles = [];
        for (final item in result ?? []) {
          pathFiles.add(
            await FileUtils.createFileFromString(
              base64String: item,
              extension: FileExtension.PDF,
            ),
          );
        }

        if (pathFiles.isEmpty) {
          yield EmptyGuidesPdfState();
        } else {
          yield DoneGetGuidesPdfState(
            guidesPath: pathFiles,
          );
        }
      } on GuideException catch (e) {
        yield ErrorGuideSolicitationState(e.message);
      } on GenericException catch (_) {
        yield ErrorGuideSolicitationState(MessageException.GENERAL);
      } catch (ex) {
        yield ErrorGuideSolicitationState(MessageException.GENERAL);
      }
    } else if (event is GetGuidesPdfV2) {
      yield LoadingGuideSolicitationState();

      try {
        final result =
            await Locator.instance.get<BeneficiaryProtocolApi>().getGuidePdfV2(
                  protocol: event.numProtocolo,
                  numAtend: event.numAtend,
                );

        final List<String> pathFiles = [];
        for (final item in result) {
          pathFiles.add(
            await FileUtils.createFileFromString(
              base64String: item,
              extension: FileExtension.PDF,
            ),
          );
        }

        if (pathFiles.isEmpty) {
          yield EmptyGuidesPdfState();
        } else {
          yield DoneGetGuidesPdfState(
            guidesPath: pathFiles,
          );
        }
      } catch (ex) {
        yield ErrorGuideSolicitationState(ex.toString());
      }
    }
  }
}
