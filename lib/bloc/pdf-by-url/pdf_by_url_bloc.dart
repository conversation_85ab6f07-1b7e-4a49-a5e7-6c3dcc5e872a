import 'dart:async';
import 'dart:io' as io;
import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/shared/api/pdf.api.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:equatable/equatable.dart';

part 'pdf_by_url_event.dart';
part 'pdf_by_url_state.dart';

// TODO trocar nomes byURL por um nome mais geral , pois agora esse bloc gerar o pdf tanto pela url como pelo path.
class PdfByUrlBloc extends Bloc<PdfByUrlEvent, PdfByUrlState> {
  PdfByUrlBloc({required this.pdfApi}) : super(PdfByUrlInitial());
  final PdfApi pdfApi;
  final logger = UnimedLogger(className: 'PdfByUrlBloc');

  @override
  Stream<PdfByUrlState> mapEventToState(
    PdfByUrlEvent event,
  ) async* {
    if (event is SetPDFByUrlError) {
      yield PdfByUrlError(event.message);
    } else if (event is GetPDFByUrl) {
      yield* _handleGetPDFByUrl(event);
    } else if (event is GetPDFByPath) {
      yield* _handleGetPDFByPath(event);
    } else if (event is ResetState) {
      yield PdfByUrlInitial();
    }
  }

  Stream<PdfByUrlState> _handleGetPDFByPath(GetPDFByPath event) async* {
    try {
      logger.d('Carregando PDF do caminho: ${event.path}');
      yield PdfByUrlLoading();

      // Verifica se o arquivo existe e é válido
      final originalFile = io.File(event.path);
      if (!await originalFile.exists()) {
        logger.e('Arquivo não encontrado: ${event.path}');
        yield PdfByUrlError('Arquivo não encontrado: ${event.path}');
        return;
      }

      // Verifica tamanho do arquivo
      final fileSize = await originalFile.length();
      logger.d('Tamanho do arquivo: ${fileSize} bytes');

      if (fileSize < 100) { // PDF muito pequeno, provavelmente corrompido
        logger.e('Arquivo muito pequeno (${fileSize} bytes), provavelmente corrompido');
        yield PdfByUrlError('Arquivo PDF muito pequeno ou corrompido');
        return;
      }

      // Verifica se é um PDF válido
      final fileBytes = await originalFile.readAsBytes();
      if (!_isValidPDF(fileBytes)) {
        logger.e('Arquivo não é um PDF válido - header incorreto');
        yield PdfByUrlError('Arquivo PDF inválido ou corrompido');
        return;
      }

      logger.d('PDF válido carregado com sucesso: ${event.path}');
      // Para arquivos locais, usa o arquivo original diretamente
      // Evita cópia desnecessária que pode corromper o arquivo
      yield PdfByUrlDone(file: originalFile);
    } catch (e) {
      logger.e('Erro ao carregar PDF: $e');
      final String message = 'Não foi possível carregar o documento: $e';
      yield PdfByUrlError(message);
    }
  }

  Stream<PdfByUrlState> _handleGetPDFByUrl(GetPDFByUrl event) async* {
    try {
      yield PdfByUrlLoading();
      final _filename = FileUtils.formatFilenamePDF(event.filename);
      io.File _file;
      _file = event.fromFirebase
          ? await pdfApi.getFirebaseFile(event.url, _filename)
          : await pdfApi.createPDFFileFromUrl(
              event.url,
              _filename,
              event.headers,
            );

      // Verifica se o arquivo baixado é um PDF válido
      if (await _file.exists()) {
        final fileBytes = await _file.readAsBytes();
        if (!_isValidPDF(fileBytes)) {
          await _file.delete(); // Remove arquivo inválido
          yield PdfByUrlError('Arquivo PDF baixado está corrompido');
          return;
        }
      }

      yield PdfByUrlDone(file: _file);
    } catch (e) {
      final String message = 'Não foi possível carregar o documento: $e';
      yield PdfByUrlError(message);
    }
  }

  // Valida se o arquivo é um PDF válido verificando o header
  bool _isValidPDF(List<int> bytes) {
    if (bytes.length < 4) return false;

    // PDF files start with %PDF
    return bytes[0] == 0x25 && // %
           bytes[1] == 0x50 && // P
           bytes[2] == 0x44 && // D
           bytes[3] == 0x46;   // F
  }
}
