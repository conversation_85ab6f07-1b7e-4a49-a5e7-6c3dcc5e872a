import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
abstract class ResExamResultDetailEvent extends Equatable {}

class GetResultDetailEvent extends ResExamResultDetailEvent {
  final String cpf;
  final String code;
  final String description;
  final String dateExam;

  @override
  List<Object> get props => [cpf, code, description, dateExam];

  GetResultDetailEvent({
    required this.cpf,
    required this.code,
    required this.description,
    required this.dateExam,
  });
}

class GetImageExamDetailsEvent extends ResExamResultDetailEvent {
  final String cpf;
  final String dataId;

  @override
  List<Object> get props => [dataId];

  GetImageExamDetailsEvent({
    required this.cpf,
    required this.dataId,
  });
}
