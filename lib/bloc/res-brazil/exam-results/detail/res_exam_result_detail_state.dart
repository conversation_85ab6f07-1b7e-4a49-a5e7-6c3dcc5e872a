import 'package:cliente_minha_unimed/models/res/exam/res_exam_result_image_detail_model.dart';
import 'package:cliente_minha_unimed/models/res/exam/res_exam_result_laboratory_detail_model.dart';
import 'package:equatable/equatable.dart';

abstract class ResExamResultDetailState extends Equatable {
  const ResExamResultDetailState();

  @override
  List<Object> get props => [];
}

class InitialResExamResultDetailState extends ResExamResultDetailState {}

class LoadingResExamResultDetailState extends ResExamResultDetailState {
  @override
  List<Object> get props => [];
}

class ErrorResExamResultDetailState extends ResExamResultDetailState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResExamResultDetailState({required this.message});
}

class LoadedResExamResultLaboratoryDetailState
    extends ResExamResultDetailState {
  final ResExamResultLaboratoryDetailModel resExamResultLaboratoryDetailModel;

  @override
  List<Object> get props => [resExamResultLaboratoryDetailModel];

  const LoadedResExamResultLaboratoryDetailState(
      {required this.resExamResultLaboratoryDetailModel});
}

class LoadedImageExamDetailsState extends ResExamResultDetailState {
  final ResExamResultImageDetailModel resImageExamDetails;

  @override
  List<Object> get props => [];

  const LoadedImageExamDetailsState({required this.resImageExamDetails});
}

class LoadedExamDetailsState extends ResExamResultDetailState {
  final ResExamResultImageDetailModel resExamDetails;

  @override
  List<Object> get props => [];

  const LoadedExamDetailsState({required this.resExamDetails});
}
