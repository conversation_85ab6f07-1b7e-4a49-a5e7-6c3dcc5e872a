import 'package:cliente_minha_unimed/bloc/res-brazil/exam-results/detail/res_exam_result_detail_event.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/exam-results/detail/res_exam_result_detail_state.dart';
import 'package:cliente_minha_unimed/models/res/exam/res_exam_result_laboratory_detail_model.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResExamResultDetailBloc
    extends Bloc<ResExamResultDetailEvent, ResExamResultDetailState> {
  ResExamResultDetailBloc() : super(InitialResExamResultDetailState());

  @override
  Stream<ResExamResultDetailState> mapEventToState(
      ResExamResultDetailEvent event) async* {
    if (event is GetResultDetailEvent) {
      try {
        yield LoadingResExamResultDetailState();

        final ResExamResultLaboratoryDetailModel
            _resExamResultLaboratoryDetailModel =
            await Locator.instance<ResApi>().searchForLaboratoryTestDetails(
          cpf: event.cpf,
          code: event.code,
          description: event.description,
          dateExam: event.dateExam,
        );

        if (_resExamResultLaboratoryDetailModel.exams.isEmpty) {
          yield ErrorResExamResultDetailState(
            message: 'Não foi possível carregar os detalhes do exame',
          );
          return;
        }

        yield LoadedResExamResultLaboratoryDetailState(
          resExamResultLaboratoryDetailModel:
              _resExamResultLaboratoryDetailModel,
        );
      } catch (e) {
        yield ErrorResExamResultDetailState(message: e.toString());
      }
    } else if (event is GetImageExamDetailsEvent) {
      try {
        yield LoadingResExamResultDetailState();

        final _resImageExamDetails = await Locator.instance<ResApi>()
            .searchForImageTestDetails(cpf: event.cpf, dataId: event.dataId);

        yield LoadedImageExamDetailsState(
          resImageExamDetails: _resImageExamDetails,
        );
      } catch (e) {
        yield ErrorResExamResultDetailState(message: e.toString());
      }
    }
  }
}
