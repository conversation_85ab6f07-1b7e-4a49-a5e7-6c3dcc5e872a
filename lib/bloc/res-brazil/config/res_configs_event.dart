import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
abstract class ResConfigEvent extends Equatable {}

class GetResConfigEvent extends ResConfigEvent {
  final String card;
  final String cpf;

  @override
  List<Object> get props => [card, cpf];

  GetResConfigEvent({required this.card, required this.cpf});
}

class GetResConfigPopupEvent extends ResConfigEvent {
  final String card;
  final String cpf;

  @override
  List<Object> get props => [card, cpf];

  GetResConfigPopupEvent({required this.card, required this.cpf});
}
