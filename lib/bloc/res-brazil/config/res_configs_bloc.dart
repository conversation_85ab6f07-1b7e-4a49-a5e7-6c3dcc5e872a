import 'dart:convert';

import 'package:cliente_minha_unimed/bloc/res-brazil/config/res_configs_event.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/config/res_configs_state.dart';
import 'package:cliente_minha_unimed/models/res/res_configs_model.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResConfigBloc extends Bloc<ResConfigEvent, ResConfigsState> {
  ResConfigBloc() : super(InitialResConfigsState());

  late ResConfigModel _resConfigModel;
  ResConfigModel get resConfigModel => _resConfigModel;

  @override
  Stream<ResConfigsState> mapEventToState(ResConfigEvent event) async* {
    if (event is GetResConfigEvent) {
      try {
        yield LoadingResConfigsState();

        _resConfigModel = await Locator.instance<ResApi>().resConfigs(card: event.card, cpf: event.cpf);
       

        yield LoadedResConfigsState(resConfigModel: _resConfigModel);
      } catch (e) {
        yield ErrorResConfigsState(message: e.toString());
      }
    } else if (event is GetResConfigPopupEvent) {
      try {
        yield LoadingResConfigsState();

        _resConfigModel = await Locator.instance<ResApi>().resConfigsPopup(card: event.card, cpf: event.cpf);

        yield LoadedResConfigsState(resConfigModel: _resConfigModel);
      } catch (e) {
        yield ErrorResConfigsState(message: e.toString());
      }
    }
  }
}
