import 'package:cliente_minha_unimed/models/res/diagnostic/detail/diagnostic_details.model.dart';
import 'package:cliente_minha_unimed/models/res/res_brazil_attendance.model.dart';
import 'package:cliente_minha_unimed/models/res/res_documents.model.dart';
import 'package:cliente_minha_unimed/models/res/res_exam.model.dart';
import 'package:cliente_minha_unimed/models/res/res_image_exam.model.dart';
import 'package:cliente_minha_unimed/models/res/res_phisical_exam.model.dart';
import 'package:equatable/equatable.dart';

abstract class ResBrazilAttendanceState extends Equatable {
  const ResBrazilAttendanceState();

  @override
  List<Object> get props => [];
}

class InitialBrazilAttendanceState extends ResBrazilAttendanceState {}

class LoadingBrazilAttendanceState extends ResBrazilAttendanceState {
  @override
  List<Object> get props => [];
}

class LoadedAttendancesState extends ResBrazilAttendanceState {
  final List<ResBrazilAttendanceModel> resAttendances;

  @override
  List<Object> get props => [];

  const LoadedAttendancesState({required this.resAttendances});
}

class LoadedDiagnosticsDetailsState extends ResBrazilAttendanceState {
  final DiagnosticDetailsModel resDiagnosticsDetails;

  @override
  List<Object> get props => [];

  const LoadedDiagnosticsDetailsState({required this.resDiagnosticsDetails});
}

class LoadedImageExamDetailsState extends ResBrazilAttendanceState {
  final ResImageExamModel resImageExamDetails;

  @override
  List<Object> get props => [];

  const LoadedImageExamDetailsState({required this.resImageExamDetails});
}

class LoadedExamDetailsState extends ResBrazilAttendanceState {
  final ResExamModel resExamDetails;

  @override
  List<Object> get props => [];

  const LoadedExamDetailsState({required this.resExamDetails});
}

class LoadedDocumentDetailsState extends ResBrazilAttendanceState {
  final ResDocumentsModel resDocumentDetails;

  @override
  List<Object> get props => [];

  const LoadedDocumentDetailsState({required this.resDocumentDetails});
}

class LoadedPhisicalExamDetailsState extends ResBrazilAttendanceState {
  final ResPhisicalExamModel resPhisicalExamDetails;

  @override
  List<Object> get props => [];

  const LoadedPhisicalExamDetailsState({required this.resPhisicalExamDetails});
}

class ErrorBrazilAttendanceState extends ResBrazilAttendanceState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorBrazilAttendanceState({required this.message});
}

class NoDataBrazilAttendanceState extends ResBrazilAttendanceState {
 
  @override
  List<Object> get props => [];
  
}
