import 'package:cliente_minha_unimed/bloc/res-brazil/attendance/res_attendance_event.dart';
import 'package:cliente_minha_unimed/bloc/res-brazil/attendance/res_attendance_state.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResBrazilAttendancesBloc
    extends Bloc<ResBrazilAttendanceEvent, ResBrazilAttendanceState> {
  ResBrazilAttendancesBloc() : super(InitialBrazilAttendanceState());

  @override
  Stream<ResBrazilAttendanceState> mapEventToState(
      ResBrazilAttendanceEvent event) async* {
    if (event is GetAttendancesEvent) {
      try {
        yield LoadingBrazilAttendanceState();

        final attendances = await Locator.instance<ResApi>().resGetAttendances(
          cpf: event.cpf,
          dateTimeRange: event.dateTimeRange,
        );

        if (attendances.isEmpty) {
          yield NoDataBrazilAttendanceState();
        } else {
          yield LoadedAttendancesState(resAttendances: attendances);
        }
      } catch (e) {
        yield ErrorBrazilAttendanceState(message: e.toString());
      }
    } else if (event is GetDiagnosticsDetailsEvent) {
      try {
        yield LoadingBrazilAttendanceState();

        final diagnosticsDetails =
            await Locator.instance<ResApi>().resGetDiagnosticDetails(
          cpf: event.cpf,
          dataId: event.dataId,
        );

        yield LoadedDiagnosticsDetailsState(
            resDiagnosticsDetails: diagnosticsDetails);
      } catch (e) {
        yield ErrorBrazilAttendanceState(message: e.toString());
      }
    } else if (event is GetImageExamDetailsEvent) {
      try {
        yield LoadingBrazilAttendanceState();

        final imageExamDetails =
            await Locator.instance<ResApi>().resGetImageExamDetails(
          cpf: event.cpf,
          dataId: event.dataId,
        );

        yield LoadedImageExamDetailsState(
            resImageExamDetails: imageExamDetails);
      } catch (e) {
        yield ErrorBrazilAttendanceState(message: e.toString());
      }
    } else if (event is GetExamDetailsEvent) {
      try {
        yield LoadingBrazilAttendanceState();

        final imageDetails = await Locator.instance<ResApi>().resGetExamDetails(
          cpf: event.cpf,
          dataId: event.dataId,
        );

        yield LoadedExamDetailsState(resExamDetails: imageDetails);
      } catch (e) {
        yield ErrorBrazilAttendanceState(message: e.toString());
      }
    } else if (event is GetDocumentDetailsEvent) {
      try {
        yield LoadingBrazilAttendanceState();

        final documentDetails =
            await Locator.instance<ResApi>().resGetDocumentDetails(
          cpf: event.cpf,
          dataId: event.dataId,
        );

        yield LoadedDocumentDetailsState(resDocumentDetails: documentDetails);
      } catch (e) {
        yield ErrorBrazilAttendanceState(message: e.toString());
      }
    } else if (event is GetPhisicalExamDetailsEvent) {
      try {
        yield LoadingBrazilAttendanceState();

        final phisicalExamDetails =
            await Locator.instance<ResApi>().resGetPhisicalExamDetails(
          cpf: event.cpf,
          dataId: event.dataId,
        );

        yield LoadedPhisicalExamDetailsState(
            resPhisicalExamDetails: phisicalExamDetails);
      } catch (e) {
        yield ErrorBrazilAttendanceState(message: e.toString());
      }
    }
  }
}
