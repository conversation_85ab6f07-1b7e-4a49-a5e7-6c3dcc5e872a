import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
abstract class ResBrazilAttendanceEvent extends Equatable {}

class GetAttendancesEvent extends ResBrazilAttendanceEvent {
  final String cpf;
  final DateTimeRange? dateTimeRange;

  @override
  List<Object> get props => [];

  GetAttendancesEvent({
    required this.cpf,
    this.dateTimeRange,
  });
}

class GetDiagnosticsDetailsEvent extends ResBrazilAttendanceEvent {
  final String cpf;
  final String dataId;

  @override
  List<Object> get props => [dataId];

  GetDiagnosticsDetailsEvent({
    required this.cpf,
    required this.dataId,
  });
}

class GetImageExamDetailsEvent extends ResBrazilAttendanceEvent {
  final String cpf;
  final String dataId;

  @override
  List<Object> get props => [dataId];

  GetImageExamDetailsEvent({
    required this.cpf,
    required this.dataId,
  });
}

class GetExamDetailsEvent extends ResBrazilAttendanceEvent {
  final String cpf;
  final String dataId;

  @override
  List<Object> get props => [dataId];

  GetExamDetailsEvent({
    required this.cpf,
    required this.dataId,
  });
}

class GetDocumentDetailsEvent extends ResBrazilAttendanceEvent {
  final String cpf;
  final String dataId;

  @override
  List<Object> get props => [dataId];

  GetDocumentDetailsEvent({
    required this.cpf,
    required this.dataId,
  });
}

class GetPhisicalExamDetailsEvent extends ResBrazilAttendanceEvent {
  final String cpf;
  final String dataId;

  @override
  List<Object> get props => [dataId];

  GetPhisicalExamDetailsEvent({
    required this.cpf,
    required this.dataId,
  });
}
