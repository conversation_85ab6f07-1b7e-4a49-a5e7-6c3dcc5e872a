import 'dart:io';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cliente_minha_unimed/bloc/pdf-by-url/pdf_by_url_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/app_bar_unimed.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:share/share.dart';

const TIME_ANIMATION_PAGE = 250;

class PDFViewScreen extends StatefulWidget {
  PDFViewScreen(
    this.url, {
    this.share = true,
    this.filename = "",
    this.title = "Visualização de arquivos",
    this.subtitle,
    this.isPath = false,
    this.parseUrl = false,
    this.onDocumentLoad,
    this.headers,
    this.onAccepted,
    this.onRejected,
    this.isAppBarVisible = true,
  });
  final String url;
  final Function? onDocumentLoad;
  final bool? share;
  final String filename;
  final String title;
  final String? subtitle;
  final bool isPath;
  final bool parseUrl;
  final Function? onAccepted;
  final Function? onRejected;
  final Map<String, String>? headers;
  final bool isAppBarVisible;
  @override
  _PDFViewScreenState createState() => _PDFViewScreenState();
}

class _PDFViewScreenState extends State<PDFViewScreen> {
  final logger = UnimedLogger(className: 'PDFViewScreen');
  File? _file;
  bool shareIsEnable = true;
  bool _isLoading = true;
  int totalPages = 0;
  int indexPage = 0;
  bool isError = false;
  bool showControllers = false;
  late PDFViewController pdfViewerController;

  _enableRotation() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void initState() {
    widget.isPath
        ? BlocProvider.of<PdfByUrlBloc>(context).add(GetPDFByPath(
            path: widget.url,
            filename: widget.filename,
          ))
        : BlocProvider.of<PdfByUrlBloc>(context).add(GetPDFByUrl(
            url: widget.url,
            headers: widget.headers,
            filename: widget.filename,
            fromFirebase: widget.parseUrl));

    _enableRotation();

    super.initState();
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(builder: (context, orientation) {
      //if (widget.isPath && _isLoading) _loadDocument();
      return Scaffold(
        appBar: _isLandscapeMode(orientation) || !widget.isAppBarVisible
            ? null
            : AppBarUnimed(
                title: Column(
                  children: [
                    AutoSizeText(widget.title, minFontSize: 12, maxLines: 1),
                    if (widget.subtitle != null && widget.subtitle!.isNotEmpty)
                      AutoSizeText(widget.subtitle!,
                          style: TextStyle(fontSize: 12),
                          minFontSize: 10,
                          maxLines: 1),
                  ],
                ),
                actions: [
                  // _iconChangeOrientations(orientation),

                  _iconShare(),
                ],
              ),
        body: SafeArea(
          child: Center(
            child: BlocBuilder<PdfByUrlBloc, PdfByUrlState>(
              buildWhen: (previous, current) {
                if (previous is PdfByUrlLoading && current is PdfByUrlError)
                  _openDialog();
                if (current is! PdfByUrlLoading) {
                  _isLoading = false;
                }

                return true;
              },
              builder: (context, state) {
                if (state is PdfByUrlLoading)
                  return CircularProgressIndicator();
                else if (state is PdfByUrlDone) {
                  _file = state.file;

                  return Center(
                    child: _viewPdf(orientation, state.file.path),
                  );
                } else
                  return Container();
              },
            ),
          ),
        ),
      );
    });
  }

  Widget _safeArea(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
        child: Wrap(
          alignment: WrapAlignment.center,
          children: <Widget>[
            ElevatedButton(
              child: Text('Confirmar'),
              onPressed: () {
                widget.onAccepted!();
              },
            ),
            Container(
              width: 20,
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: Text('Rejeitar'),
              onPressed: () {
                widget.onRejected!();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _viewPdf(Orientation orientation, String filePath) {
    return isError
        ? Center(child: EvaTriste(message: Text(MessageException.GENERAL)))
        : Stack(
            children: [
              _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : Column(
                      children: [
                        Expanded(
                            child: PDFView(
                          pageFling: false,
                          pageSnap: false,
                          enableSwipe: false,
                          filePath: filePath,
                          autoSpacing: true,
                          onViewCreated: (PdfController) {
                            if (widget.onDocumentLoad != null) {
                              widget.onDocumentLoad!();
                            }
                            setState(() {
                              pdfViewerController = PdfController;
                            });
                            // Força uma atualização após a criação da view (correção Android)
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (mounted) setState(() {});
                            });
                          },
                          onRender: (pages) {
                            setState(() {
                              totalPages = pages!;
                              showControllers = true;
                            });
                            // Força atualização após render (correção Android)
                            Future.delayed(Duration(milliseconds: 100), () {
                              if (mounted) setState(() {});
                            });
                          },
                        )),
                        _rowPdfControllers(context)
                      ],
                    ),
            ],
          );
  }

  Widget customContainer(child) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.grey[700]!.withAlpha(200),
        borderRadius: BorderRadius.all(Radius.circular(5)),
      ),
      child: child,
    );
  }

  Widget _iconShare() {
    return widget.share != null
        ? Semantics(
            label: 'Compartilhar',
            child: IconButton(
              icon: Icon(Icons.share, color: Colors.white),
              onPressed: widget.share! ? _urlFileShare : null,
            ),
          )
        : Container();
  }

  // Widget _iconChangeOrientations(orientation) {
  //   return IconButton(
  //     icon: Icon(Icons.crop_rotate_rounded, color: Colors.white),
  //     onPressed: () {
  //       orientation.index == DeviceOrientation.portraitUp.index
  //           ? SystemChrome.setPreferredOrientations(
  //               [DeviceOrientation.landscapeLeft])
  //           : SystemChrome.setPreferredOrientations(
  //               [DeviceOrientation.portraitUp]);
  //     },
  //   );
  // }

  bool _isLandscapeMode(orientation) =>
      orientation.index == DeviceOrientation.landscapeLeft.index;

  _openDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => UnimedAlertDialog(
        textWidget:
            Text('Não foi possível carregar PDF', textAlign: TextAlign.center),
        onPressed: () {
          Navigator.of(context).pop();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  Widget _rowPdfControllers(BuildContext context) {
    return showControllers
        ? Container(
            child: Column(
              children: [
                if (widget.onAccepted != null) _safeArea(context),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 6),
                  color: UnimedColors.green,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: (indexPage != 0)
                              ? () {
                                  setState(() {
                                    pdfViewerController.setPage(0);
                                  });
                                }
                              : null,
                          child: Icon(
                            Icons.first_page,
                            color:
                                indexPage != 0 ? Colors.white : Colors.black45,
                            size: 32,
                          ),
                        ),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: indexPage != 0
                              ? () {
                                  setState(() {
                                    indexPage = indexPage - 1;
                                    pdfViewerController.setPage(indexPage);
                                  });
                                }
                              : null,
                          child: Icon(
                            Icons.chevron_left,
                            size: 32,
                            color:
                                indexPage != 0 ? Colors.white : Colors.black45,
                          ),
                        ),
                      ),
                      Text(
                        '${indexPage + 1}/${totalPages}',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: (indexPage + 1) < totalPages
                              ? () {
                                  setState(() {
                                    indexPage = indexPage + 1;
                                    pdfViewerController.setPage(indexPage);
                                  });
                                }
                              : null,
                          child: Icon(
                            Icons.chevron_right,
                            size: 32,
                            color: indexPage != totalPages - 1
                                ? Colors.white
                                : Colors.black45,
                          ),
                        ),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: (indexPage + 1) != totalPages
                              ? () {
                                  setState(() {
                                    indexPage = totalPages - 1;
                                    pdfViewerController.setPage(indexPage);
                                  });
                                }
                              : null,
                          child: Icon(
                            Icons.last_page,
                            color: (indexPage + 1) != totalPages
                                ? Colors.white
                                : Colors.black45,
                            size: 32,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )
        : Container();
  }

  Future<void> _urlFileShare() async {
    setState(() => shareIsEnable = false);
    final String _path;
    if (widget.isPath) {
      _path = widget.url;
    } else {
      if (_file == null) return;
      _path = _file!.path;
    }
    try {
      await Share.shareFiles([_path]);
    } catch (ex) {
      logger.e('_urlFileShare catch exception - $ex - url: ${widget.url}');
      setState(() => shareIsEnable = true);
    } finally {
      Future.delayed(Duration(milliseconds: 1500),
          () => setState(() => shareIsEnable = true));
    }
  }
}
