import 'dart:io';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:http_client/http_client.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

class PdfApi {
  final UnimedHttpClient httpClient;
  final UnimedLogger logger;

  PdfApi({required this.httpClient, required this.logger});

  Future<File> createPDFFileFromUrl(
      String url, String filename, Map<String, String>? headers) async {
    try {
      final documentDirectory = (await getApplicationDocumentsDirectory()).path;
      File file = File('$documentDirectory' +
          '/${DateTime.now().millisecondsSinceEpoch.toString()}_$filename');
      final _headers = {
        'Accept': 'application/pdf',
      };
      if (headers != null) _headers.addAll(headers);

      final response = await httpClient.get(Uri.parse(url), headers: _headers);
      if (response.statusCode == 200) {
        final _file = file.writeAsBytes(response.bodyBytes);
        return _file;
      } else {
        logger.e(
            'createPDFFileFromUrl - statusCode : ${response.statusCode} ${response.body}');
        throw PdfException('Serviço indisponível no momento.');
      }
    } on NoInternetException catch (error) {
      logger.e('_getFileFromUrl NoInternetException $error');
      throw error;
    } on ServiceTimeoutException catch (error) {
      logger.e('_getFileFromUrl ServiceTimeoutException $error');
      throw error;
    } on UnimedException catch (ex) {
      throw PdfException(ex.message);
    } catch (ex) {
      logger.e('_getFileFromUrl catch exception - $ex - url: $url');
      throw PdfException('Não foi possível no momento.');
    }
  }

  Future<File> getFirebaseFile(String url, String filename) async {
    try {
      final request = await HttpClient().getUrl(Uri.parse(url));
      final response = await request.close();

      final documentDirectory = (await getApplicationDocumentsDirectory()).path;
      final path = join(documentDirectory,
          '${DateTime.now().millisecondsSinceEpoch}_$filename');
      await response.pipe(File(path).openWrite());
      return File(path);
    } on SocketException catch (error) {
      logger.e('getFirebaseFile SocketException $error');
      throw NoInternetException();
    } on NoInternetException catch (error) {
      logger.e('getFirebaseFile NoInternetException $error');
      throw error;
    } on ServiceTimeoutException catch (error) {
      logger.e('getFirebaseFile ServiceTimeoutException $error');
      throw error;
    } on UnimedException catch (ex) {
      throw PdfException(ex.message);
    } catch (ex) {
      logger.e('getFirebaseFile catch exception - $ex - url: $url');
      throw PdfException('Não foi possível no momento.');
    }
  }
}
